<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Hymn extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'author',
        'composer',
        'year_written',
        'lyrics',
        'verse_structure',
        'type',
        'key_signature',
        'time_signature',
        'tempo_bpm',
        'chord_progression',
        'chord_chart_path',
        'sheet_music_path',
        'audio_path',
        'category_id',
        'themes',
        'scripture_references',
        'status',
        'is_featured',
        'published_at',
        'created_by',
        'updated_by',
        'usage_count',
        'last_used_at',
    ];

    protected function casts(): array
    {
        return [
            'year_written' => 'integer',
            'verse_structure' => 'array',
            'tempo_bpm' => 'integer',
            'themes' => 'array',
            'scripture_references' => 'array',
            'is_featured' => 'boolean',
            'published_at' => 'datetime',
            'usage_count' => 'integer',
            'last_used_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function (Hymn $hymn) {
            if (empty($hymn->slug)) {
                $hymn->slug = Str::slug($hymn->title);
            }
        });

        static::updating(function (Hymn $hymn) {
            if ($hymn->isDirty('title') && empty($hymn->slug)) {
                $hymn->slug = Str::slug($hymn->title);
            }
        });
    }

    /**
     * Get the category that owns the hymn.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ContentCategory::class, 'category_id');
    }

    /**
     * Get the user who created the hymn.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the hymn.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the chord chart URL.
     */
    public function getChordChartUrlAttribute(): ?string
    {
        return $this->chord_chart_path ? Storage::url($this->chord_chart_path) : null;
    }

    /**
     * Get the sheet music URL.
     */
    public function getSheetMusicUrlAttribute(): ?string
    {
        return $this->sheet_music_path ? Storage::url($this->sheet_music_path) : null;
    }

    /**
     * Get the audio URL.
     */
    public function getAudioUrlAttribute(): ?string
    {
        return $this->audio_path ? Storage::url($this->audio_path) : null;
    }

    /**
     * Get formatted lyrics with proper line breaks.
     */
    public function getFormattedLyricsAttribute(): string
    {
        return nl2br(e($this->lyrics));
    }

    /**
     * Scope to get published hymns.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
            ->where('published_at', '<=', now());
    }

    /**
     * Scope to get featured hymns.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get hymns by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to search hymns.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
                ->orWhere('lyrics', 'like', "%{$search}%")
                ->orWhere('author', 'like', "%{$search}%");
        });
    }

    /**
     * Increment usage count and update last used timestamp.
     */
    public function markAsUsed(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }
}
