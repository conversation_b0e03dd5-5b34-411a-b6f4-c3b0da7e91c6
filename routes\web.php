<?php

use App\Http\Controllers\ChurchSettingsController;
use App\Http\Controllers\NotificationsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::get('theme-showcase', function () {
        return Inertia::render('theme-showcase');
    })->name('theme-showcase');

    Route::get('members', function () {
        return Inertia::render('members');
    })->name('members');

    Route::get('admin/analytics', function () {
        return Inertia::render('admin/analytics');
    })->name('admin.analytics');

    Route::get('live-attendance', function () {
        return Inertia::render('live-attendance');
    })->name('live-attendance');

    Route::get('manual-checkin', function () {
        return Inertia::render('manual-checkin');
    })->name('manual-checkin');

    // Church Settings
    Route::get('church-settings', [ChurchSettingsController::class, 'index'])->name('church-settings');
    Route::put('church-settings', [ChurchSettingsController::class, 'update'])->name('church-settings.update');

    // Notifications Management
    Route::get('notifications', [NotificationsController::class, 'index'])->name('notifications');
    Route::post('notifications/send', [NotificationsController::class, 'send'])->name('notifications.send');
    Route::post('notifications/schedule', [NotificationsController::class, 'schedule'])->name('notifications.schedule');
    Route::get('notifications/history', [NotificationsController::class, 'history'])->name('notifications.history');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
