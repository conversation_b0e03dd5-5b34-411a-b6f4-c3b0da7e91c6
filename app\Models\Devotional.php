<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Devotional extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'summary',
        'author',
        'devotional_date',
        'scripture_references',
        'key_verse',
        'key_verse_reference',
        'prayer',
        'reflection_questions',
        'category_id',
        'themes',
        'series_name',
        'series_order',
        'featured_image_path',
        'audio_path',
        'status',
        'is_featured',
        'published_at',
        'scheduled_for',
        'created_by',
        'updated_by',
        'view_count',
        'share_count',
        'tags',
    ];

    protected function casts(): array
    {
        return [
            'devotional_date' => 'date',
            'scripture_references' => 'array',
            'themes' => 'array',
            'series_order' => 'integer',
            'is_featured' => 'boolean',
            'published_at' => 'datetime',
            'scheduled_for' => 'datetime',
            'view_count' => 'integer',
            'share_count' => 'integer',
            'tags' => 'array',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function (Devotional $devotional) {
            if (empty($devotional->slug)) {
                $devotional->slug = Str::slug($devotional->title);
            }
        });

        static::updating(function (Devotional $devotional) {
            if ($devotional->isDirty('title') && empty($devotional->slug)) {
                $devotional->slug = Str::slug($devotional->title);
            }
        });
    }

    /**
     * Get the category that owns the devotional.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ContentCategory::class, 'category_id');
    }

    /**
     * Get the user who created the devotional.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the devotional.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the featured image URL.
     */
    public function getFeaturedImageUrlAttribute(): ?string
    {
        return $this->featured_image_path ? Storage::url($this->featured_image_path) : null;
    }

    /**
     * Get the audio URL.
     */
    public function getAudioUrlAttribute(): ?string
    {
        return $this->audio_path ? Storage::url($this->audio_path) : null;
    }

    /**
     * Get formatted content with proper line breaks.
     */
    public function getFormattedContentAttribute(): string
    {
        return nl2br(e($this->content));
    }

    /**
     * Get excerpt from content.
     */
    public function getExcerptAttribute(): string
    {
        return $this->summary ?: Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Scope to get published devotionals.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
            ->where('published_at', '<=', now());
    }

    /**
     * Scope to get scheduled devotionals.
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled')
            ->whereNotNull('scheduled_for');
    }

    /**
     * Scope to get featured devotionals.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get devotionals by date.
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('devotional_date', $date);
    }

    /**
     * Scope to get devotionals by series.
     */
    public function scopeBySeries($query, string $series)
    {
        return $query->where('series_name', $series)->orderBy('series_order');
    }

    /**
     * Scope to search devotionals.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
                ->orWhere('content', 'like', "%{$search}%")
                ->orWhere('summary', 'like', "%{$search}%");
        });
    }

    /**
     * Increment view count.
     */
    public function incrementViews(): void
    {
        $this->increment('view_count');
    }

    /**
     * Increment share count.
     */
    public function incrementShares(): void
    {
        $this->increment('share_count');
    }
}
